from fastapi import <PERSON><PERSON><PERSON>, HTTPException ,Query
from pydantic import BaseModel
import ollama, asyncio, os
from concurrent.futures import ThreadPoolExecutor
from typing import Optional

# ------------------------------------------------------------------
# Configura<PERSON><PERSON>es
# ------------------------------------------------------------------
OLLAMA_HOST: str = os.getenv("OLLAMA_HOST", "http://127.0.0.1:11434")
MODEL_NAME: str = os.getenv("OLLAMA_MODEL", "gemma3:12b")

# Cria apenas UM executor para todo o app
executor: ThreadPoolExecutor = ThreadPoolExecutor(max_workers=1)

app = FastAPI(title="Ollama + FastAPI Gateway")

# ------------------------------------------------------------------
# Esquema de entrada
# ------------------------------------------------------------------
class Prompt(BaseModel):
    prompt: str
    system: Optional[str] = None          # prompt de sistema opcional
    temperature: float = 0.7              # hiper-parâmetro opcional

# ------------------------------------------------------------------
# Função auxiliar (executa a chamada bloqueante em outra thread)
# ------------------------------------------------------------------
async def call_ollama(payload: Prompt):
    loop = asyncio.get_running_loop()
    try:
        return await loop.run_in_executor(
            executor,
            lambda: ollama.chat(
                model=MODEL_NAME,
                messages=[
                    *(
                        [{"role": "system", "content": payload.system}]
                        if payload.system else []
                    ),
                    {"role": "user", "content": payload.prompt},
                ],
                options={"temperature": payload.temperature},
            ),
        )
    except Exception as exc:
        raise HTTPException(status_code=500, detail=str(exc))

# ------------------------------------------------------------------
# Endpoint
# ------------------------------------------------------------------
@app.post("/generate")
async def generate_get(prompt: str = Query(..., min_length=1)):
    response = await call_ollama(Prompt(prompt=prompt))
    return {"response": response["message"]["content"]}